/* 全局表格样式配置 */

/* 表格头部高度统一配置 - 48px */
.ant-table-thead > tr > th {
  height: 48px !important;
  padding: 12px !important;
  line-height: 24px !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
}

/* 确保表格头部行高度也是48px */
.ant-table-thead > tr {
  height: 48px !important;
}

/* 表格单元格高度配置 */
.ant-table-tbody > tr > td {
  padding: 12px !important;
  vertical-align: middle !important;
}

/* 表格头部固定时的样式优化 */
.ant-table-thead.ant-table-thead-sticky > tr > th {
  background: rgba(248, 250, 252, 0.95) !important;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* 确保表格头部在固定时保持正确的高度 */
.ant-table-container .ant-table-header {
  background: transparent;
}

/* 表格滚动时头部阴影效果 */
.ant-table-container.ant-table-ping-top .ant-table-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
